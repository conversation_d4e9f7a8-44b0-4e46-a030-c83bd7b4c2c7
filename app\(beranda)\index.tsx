import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import { Dimensions, SafeAreaView, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { homeScreenStyles } from './styles';

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const { user, signOut } = useAuth();

  // Mock data - in real app, this would come from database
  const savingsData = {
    currentAmount: 2500000,
    targetAmount: 10000000,
    selectedAgent: 'Ibu Sari',
    agentPhone: '0812-3456-7890'
  };

  const progressPercentage = Math.round((savingsData.currentAmount / savingsData.targetAmount) * 100);

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };



  return (
    <SafeAreaView style={homeScreenStyles.container}>
      <ScrollView
        contentContainerStyle={homeScreenStyles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Gradient */}
        <LinearGradient
          colors={['#1976D2', '#1565C0', '#0D47A1']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={homeScreenStyles.headerGradient}
        >
          <View style={homeScreenStyles.header}>
            <View style={homeScreenStyles.headerLeft}>
              <Text style={homeScreenStyles.appTitle}>💰 SaveMoney</Text>
              <Text style={homeScreenStyles.headerSubtitle}>Kelola tabungan dengan mudah</Text>
            </View>
            <TouchableOpacity style={homeScreenStyles.signOutButton} onPress={handleSignOut}>
              <Ionicons name="log-out-outline" size={22} color="#fff" />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Welcome Card */}
        <View style={homeScreenStyles.welcomeCard}>
          <View style={homeScreenStyles.welcomeHeader}>
            <View style={homeScreenStyles.avatarContainer}>
              <Ionicons name="person-circle" size={50} color="#1976D2" />
            </View>
            <View style={homeScreenStyles.welcomeInfo}>
              <Text style={homeScreenStyles.welcomeText}>Selamat Datang!</Text>
              <Text style={homeScreenStyles.userEmail}>{user?.email}</Text>
            </View>
          </View>
        </View>

        {/* Main Progress Card */}
        <View style={homeScreenStyles.mainProgressCard}>
          <LinearGradient
            colors={['#E3F2FD', '#BBDEFB', '#90CAF9']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={homeScreenStyles.progressGradient}
          >
            {/* Decorative Pattern */}
            <View style={homeScreenStyles.decorativePattern}>
              <View style={[homeScreenStyles.patternCircle, { top: -20, right: -20 }]} />
              <View style={[homeScreenStyles.patternCircle, { bottom: -30, left: -30, opacity: 0.3 }]} />
            </View>

            <View style={homeScreenStyles.progressHeader}>
              <View style={homeScreenStyles.walletIconContainer}>
                <Ionicons name="wallet" size={28} color="#1976D2" />
              </View>
              <Text style={homeScreenStyles.progressTitle}>Progress Tabungan</Text>
            </View>

            <View style={homeScreenStyles.amountDisplay}>
              <Text style={homeScreenStyles.currentAmountLabel}>Tabungan Saat Ini</Text>
              <Text style={homeScreenStyles.currentAmount}>{formatCurrency(savingsData.currentAmount)}</Text>
              <Text style={homeScreenStyles.targetAmountLabel}>dari target {formatCurrency(savingsData.targetAmount)}</Text>
            </View>

            <View style={homeScreenStyles.progressContainer}>
              <View style={homeScreenStyles.progressBar}>
                <LinearGradient
                  colors={['#1976D2', '#1565C0', '#0D47A1']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={[
                    homeScreenStyles.progressFill,
                    { width: `${Math.min(progressPercentage, 100)}%` }
                  ]}
                />
                <View style={homeScreenStyles.progressGlow} />
              </View>
              <View style={homeScreenStyles.progressBadge}>
                <LinearGradient
                  colors={['#1976D2', '#0D47A1']}
                  style={homeScreenStyles.progressBadgeGradient}
                >
                  <Text style={homeScreenStyles.progressText}>{progressPercentage}%</Text>
                </LinearGradient>
              </View>
            </View>

            {/* Motivational Text */}
            <View style={homeScreenStyles.motivationContainer}>
              <Ionicons name="star" size={16} color="#FF9800" />
              <Text style={homeScreenStyles.motivationText}>
                Hebat! Anda sudah {progressPercentage}% menuju target!
              </Text>
            </View>
          </LinearGradient>
        </View>

        {/* Savings Stats Cards */}
        <View style={homeScreenStyles.statsSection}>
          <View style={homeScreenStyles.statsGrid}>
            <View style={homeScreenStyles.statCard}>
              <LinearGradient
                colors={['#1976D2', '#1565C0']}
                style={homeScreenStyles.statGradient}
              >
                <Ionicons name="trending-up" size={28} color="#fff" />
                <Text style={homeScreenStyles.statValue}>25%</Text>
                <Text style={homeScreenStyles.statLabel}>Progress</Text>
              </LinearGradient>
            </View>

            <View style={homeScreenStyles.statCard}>
              <LinearGradient
                colors={['#0D47A1', '#1565C0']}
                style={homeScreenStyles.statGradient}
              >
                <Ionicons name="calendar" size={28} color="#fff" />
                <Text style={homeScreenStyles.statValue}>8</Text>
                <Text style={homeScreenStyles.statLabel}>Bulan Lagi</Text>
              </LinearGradient>
            </View>
          </View>
        </View>

        {/* Achievement Badge */}
        <View style={homeScreenStyles.achievementSection}>
          <LinearGradient
            colors={['#E8F5E8', '#F1F8E9']}
            style={homeScreenStyles.achievementCard}
          >
            <View style={homeScreenStyles.achievementContent}>
              <View style={homeScreenStyles.achievementIcon}>
                <Ionicons name="trophy" size={32} color="#FF9800" />
              </View>
              <View style={homeScreenStyles.achievementText}>
                <Text style={homeScreenStyles.achievementTitle}>Pencapaian Terbaru!</Text>
                <Text style={homeScreenStyles.achievementDesc}>Anda telah menabung selama 3 bulan berturut-turut</Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Agent Card */}
        <View style={homeScreenStyles.agentSection}>
          <Text style={homeScreenStyles.sectionTitle}>Agent Anda</Text>
          <View style={homeScreenStyles.agentCard}>
            <LinearGradient
              colors={['#E8F5E8', '#F1F8E9', '#E8F5E8']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={homeScreenStyles.agentGradient}
            >
              {/* Decorative Elements */}
              <View style={homeScreenStyles.agentDecoration}>
                <View style={[homeScreenStyles.decorativeDot, { top: 10, right: 15 }]} />
                <View style={[homeScreenStyles.decorativeDot, { bottom: 15, left: 10, opacity: 0.5 }]} />
              </View>

              <View style={homeScreenStyles.agentIcon}>
                <Ionicons name="person" size={32} color="#1976D2" />
                <View style={homeScreenStyles.agentBadge}>
                  <Ionicons name="checkmark" size={12} color="#fff" />
                </View>
              </View>
              <View style={homeScreenStyles.agentInfo}>
                <Text style={homeScreenStyles.agentName}>{savingsData.selectedAgent}</Text>
                <Text style={homeScreenStyles.agentPhone}>{savingsData.agentPhone}</Text>
                <View style={homeScreenStyles.agentStatus}>
                  <View style={homeScreenStyles.statusDot} />
                  <Text style={homeScreenStyles.statusText}>Online</Text>
                </View>
                <Text style={homeScreenStyles.agentExperience}>Berpengalaman 5+ tahun</Text>
              </View>
              <TouchableOpacity style={homeScreenStyles.callButton}>
                <LinearGradient
                  colors={['#4CAF50', '#45A049', '#388E3C']}
                  style={homeScreenStyles.callButtonGradient}
                >
                  <Ionicons name="call" size={24} color="#fff" />
                </LinearGradient>
              </TouchableOpacity>
            </LinearGradient>
          </View>
        </View>

        {/* Floating Action Button */}
        <TouchableOpacity style={homeScreenStyles.fab}>
          <LinearGradient
            colors={['#1976D2', '#1565C0', '#0D47A1']}
            style={homeScreenStyles.fabGradient}
          >
            <Ionicons name="add" size={28} color="#fff" />
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}