import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import { Dimensions, SafeAreaView, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { homeScreenStyles } from './styles';

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const { user, signOut } = useAuth();

  // Mock data - in real app, this would come from database
  const savingsData = {
    currentAmount: 2500000,
    targetAmount: 10000000,
    selectedAgent: 'Ibu Sari',
    agentPhone: '0812-3456-7890'
  };

  const progressPercentage = Math.round((savingsData.currentAmount / savingsData.targetAmount) * 100);

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const quickActions = [
    { icon: 'add-circle', title: 'Setor', subtitle: 'Tambah Tabungan', color: '#1976D2' },
    { icon: 'history', title: 'Riwayat', subtitle: 'Lihat Transaksi', color: '#1565C0' },
    { icon: 'trending-up', title: 'Target', subtitle: 'Atur Target', color: '#0D47A1' },
    { icon: 'calculator', title: 'Simulasi', subtitle: 'Hitung Tabungan', color: '#0277BD' },
  ];

  return (
    <SafeAreaView style={homeScreenStyles.container}>
      <ScrollView
        contentContainerStyle={homeScreenStyles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Gradient */}
        <LinearGradient
          colors={['#1976D2', '#1565C0', '#0D47A1']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={homeScreenStyles.headerGradient}
        >
          <View style={homeScreenStyles.header}>
            <View style={homeScreenStyles.headerLeft}>
              <Text style={homeScreenStyles.appTitle}>💰 SaveMoney</Text>
              <Text style={homeScreenStyles.headerSubtitle}>Kelola tabungan dengan mudah</Text>
            </View>
            <TouchableOpacity style={homeScreenStyles.signOutButton} onPress={handleSignOut}>
              <Ionicons name="log-out-outline" size={22} color="#fff" />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Welcome Card */}
        <View style={homeScreenStyles.welcomeCard}>
          <View style={homeScreenStyles.welcomeHeader}>
            <View style={homeScreenStyles.avatarContainer}>
              <Ionicons name="person-circle" size={50} color="#1976D2" />
            </View>
            <View style={homeScreenStyles.welcomeInfo}>
              <Text style={homeScreenStyles.welcomeText}>Selamat Datang!</Text>
              <Text style={homeScreenStyles.userEmail}>{user?.email}</Text>
            </View>
          </View>
        </View>

        {/* Main Progress Card */}
        <View style={homeScreenStyles.mainProgressCard}>
          <LinearGradient
            colors={['#E3F2FD', '#BBDEFB']}
            style={homeScreenStyles.progressGradient}
          >
            <View style={homeScreenStyles.progressHeader}>
              <Ionicons name="wallet" size={28} color="#1976D2" />
              <Text style={homeScreenStyles.progressTitle}>Progress Tabungan</Text>
            </View>

            <View style={homeScreenStyles.amountDisplay}>
              <Text style={homeScreenStyles.currentAmountLabel}>Tabungan Saat Ini</Text>
              <Text style={homeScreenStyles.currentAmount}>{formatCurrency(savingsData.currentAmount)}</Text>
              <Text style={homeScreenStyles.targetAmountLabel}>dari target {formatCurrency(savingsData.targetAmount)}</Text>
            </View>

            <View style={homeScreenStyles.progressContainer}>
              <View style={homeScreenStyles.progressBar}>
                <LinearGradient
                  colors={['#1976D2', '#1565C0']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={[
                    homeScreenStyles.progressFill,
                    { width: `${Math.min(progressPercentage, 100)}%` }
                  ]}
                />
              </View>
              <View style={homeScreenStyles.progressBadge}>
                <Text style={homeScreenStyles.progressText}>{progressPercentage}%</Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Quick Actions Grid */}
        <View style={homeScreenStyles.quickActionsSection}>
          <Text style={homeScreenStyles.sectionTitle}>Menu Cepat</Text>
          <View style={homeScreenStyles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <TouchableOpacity key={index} style={homeScreenStyles.quickActionCard}>
                <LinearGradient
                  colors={[action.color, `${action.color}CC`]}
                  style={homeScreenStyles.quickActionGradient}
                >
                  <MaterialIcons name={action.icon as any} size={32} color="#fff" />
                </LinearGradient>
                <Text style={homeScreenStyles.quickActionTitle}>{action.title}</Text>
                <Text style={homeScreenStyles.quickActionSubtitle}>{action.subtitle}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Agent Card */}
        <View style={homeScreenStyles.agentSection}>
          <Text style={homeScreenStyles.sectionTitle}>Agent Anda</Text>
          <View style={homeScreenStyles.agentCard}>
            <LinearGradient
              colors={['#E8F5E8', '#F1F8E9']}
              style={homeScreenStyles.agentGradient}
            >
              <View style={homeScreenStyles.agentIcon}>
                <Ionicons name="person" size={32} color="#1976D2" />
              </View>
              <View style={homeScreenStyles.agentInfo}>
                <Text style={homeScreenStyles.agentName}>{savingsData.selectedAgent}</Text>
                <Text style={homeScreenStyles.agentPhone}>{savingsData.agentPhone}</Text>
                <View style={homeScreenStyles.agentStatus}>
                  <View style={homeScreenStyles.statusDot} />
                  <Text style={homeScreenStyles.statusText}>Online</Text>
                </View>
              </View>
              <TouchableOpacity style={homeScreenStyles.callButton}>
                <LinearGradient
                  colors={['#4CAF50', '#45A049']}
                  style={homeScreenStyles.callButtonGradient}
                >
                  <Ionicons name="call" size={24} color="#fff" />
                </LinearGradient>
              </TouchableOpacity>
            </LinearGradient>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}